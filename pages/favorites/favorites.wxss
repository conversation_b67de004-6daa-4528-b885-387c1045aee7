/* pages/favorites/favorites.wxss */
.scrollarea {
  height: 100vh;
  background-color: #f5f5f5;
}

.favorites-header-spacer {
  height: 120rpx;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx 32rpx 120rpx 32rpx; /* 减少顶部padding */
  box-sizing: border-box;
}

/* 收藏列表样式 */
.favorites-list {
  width: 100%;
}

.favorite-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  margin-bottom: 20rpx;
  transition: all 0.2s ease;
}

.favorite-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0,0,0,0.1);
}

.favorite-image {
  width: 96rpx;
  height: 96rpx;
  border-radius: 18rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 56rpx;
  background-color: #fff3f0;
}

.favorite-info {
  flex: 1;
}

.favorite-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}

.favorite-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.favorite-btn {
  padding: 16rpx;
  color: #ff6b6b;
  font-size: 32rpx;
  transition: transform 0.2s ease;
}

.favorite-btn:active {
  transform: scale(1.2);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  color: #ddd;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  color: #666;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.go-explore-btn {
  padding: 24rpx 48rpx;
  background: linear-gradient(90deg, #ff7e2d 0%, #ff3d2d 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 61, 45, 0.3);
  transition: all 0.2s ease;
}

.go-explore-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 61, 45, 0.3);
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 40rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #ccc;
}

/* 收藏按钮样式优化 */
.favorite-icon {
  color: #ff4757;
  font-size: 36rpx;
}
